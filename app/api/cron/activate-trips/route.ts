import { NextRequest, NextResponse } from "next/server"
import { getAdminInstance } from "@/lib/firebase-admin"
import { NotificationService } from "@/lib/domains/notification/notification.service"

// Rate limiting and origin validation for security
const ALLOWED_ORIGINS = [
  "https://vercel.com",
  "https://cron-job.vercel.app",
  process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : null,
].filter(Boolean)

const CRON_SECRET = process.env.CRON_SECRET || "default-secret"

/**
 * Validate cron job request for security
 */
function validateCronRequest(request: NextRequest): { isValid: boolean; error?: string } {
  // Validate cron secret for security
  const authHeader = request.headers.get("authorization")
  const providedSecret = authHeader?.replace("Bearer ", "")
  
  if (providedSecret !== CRON_SECRET) {
    console.error("Unauthorized cron job access attempt")
    return { isValid: false, error: "Unauthorized" }
  }

  // Validate origin for additional security
  const origin = request.headers.get("origin")
  const userAgent = request.headers.get("user-agent")
  
  // Allow Vercel cron jobs (they don't always have origin headers)
  const isVercelCron = userAgent?.includes("vercel") || !origin
  const isAllowedOrigin = origin && ALLOWED_ORIGINS.includes(origin)
  
  if (!isVercelCron && !isAllowedOrigin) {
    console.error("Invalid origin for cron job:", origin)
    return { isValid: false, error: "Invalid origin" }
  }

  return { isValid: true }
}

/**
 * Activate trips that should become active (planning → active)
 * This function is idempotent and safe to run multiple times
 */
async function activateTrips() {
  console.log("Starting trip activation process...")

  // Get Firebase Admin instance
  const { adminDb } = await getAdminInstance()
  if (!adminDb) {
    throw new Error("Firebase Admin not initialized")
  }

  // Get current date in UTC
  const now = new Date()
  const currentDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())

  const tripsRef = adminDb.collection("trips")
  const activatedTrips: any[] = []
  const batch = adminDb.batch()

  // Check for trips that should become active (planning → active)
  console.log("Checking for trips to activate...")
  const planningTripsQuery = tripsRef.where("status", "==", "planning")
  const planningTripsSnapshot = await planningTripsQuery.get()

  for (const tripDoc of planningTripsSnapshot.docs) {
    const tripData = tripDoc.data()
    const tripId = tripDoc.id

    // Convert Firestore timestamp to Date
    const startDate = tripData.startDate?.toDate()
    if (!startDate) continue

    // Check if trip should be activated (startDate <= current date)
    const tripStartDate = new Date(
      startDate.getFullYear(),
      startDate.getMonth(),
      startDate.getDate()
    )

    if (tripStartDate <= currentDate) {
      console.log(`Activating trip ${tripId} (started: ${tripStartDate.toISOString()})`)

      // Update trip status to active
      const tripRef = adminDb.collection("trips").doc(tripId)
      batch.update(tripRef, {
        status: "active",
        updatedAt: now,
      })

      // Get trip attendees for notifications
      const userTripsRef = adminDb.collection("userTrips")
      const userTripsQuery = userTripsRef
        .where("tripId", "==", tripId)
        .where("status", "==", "going")
      const userTripsSnapshot = await userTripsQuery.get()

      const attendeeIds = userTripsSnapshot.docs.map((doc: any) => doc.data().userId)

      activatedTrips.push({
        tripId,
        tripName: tripData.name || "Unknown Trip",
        destination: tripData.destination || "Unknown Destination",
        squadId: tripData.squadId,
        attendeeIds,
      })
    }
  }

  // Commit the batch update (idempotent - safe to run multiple times)
  if (activatedTrips.length > 0) {
    await batch.commit()
    console.log(`Updated ${activatedTrips.length} trips to active status`)
  }

  // Send notifications for activated trips
  const activationNotificationResults = []

  for (const trip of activatedTrips) {
    try {
      // Create notifications for all trip members about trip activation
      for (const userId of trip.attendeeIds) {
        try {
          await NotificationService.createNotification(userId, {
            userId,
            type: "trip_update",
            title: "Trip Started! 🚀",
            message: `Your trip to ${trip.destination} has started! Have an amazing time and stay safe.`,
            read: false,
            actionUrl: `/trips/${trip.tripId}`,
            relatedEntityId: trip.tripId,
            relatedEntityType: "trip",
          })
        } catch (notifError) {
          console.error(`Error creating activation notification for user ${userId}:`, notifError)
        }
      }

      activationNotificationResults.push({
        tripId: trip.tripId,
        notificationsSent: trip.attendeeIds.length,
      })
    } catch (error) {
      console.error(`Error processing notifications for trip ${trip.tripId}:`, error)
    }
  }

  return {
    tripsProcessed: planningTripsSnapshot.size,
    tripsActivated: activatedTrips.length,
    activatedTrips: activatedTrips.map((t) => ({
      tripId: t.tripId,
      destination: t.destination,
      attendeeCount: t.attendeeIds.length,
    })),
    activationNotificationResults,
  }
}

export async function GET(request: NextRequest) {
  try {
    const validation = validateCronRequest(request)
    if (!validation.isValid) {
      return NextResponse.json({ error: validation.error }, { status: validation.error === "Unauthorized" ? 401 : 403 })
    }

    const result = await activateTrips()

    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      ...result,
    }

    console.log("Trip activation completed:", response)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error in trip activation cron job:", error)
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}

// Also handle POST requests for manual testing
export async function POST(request: NextRequest) {
  return GET(request)
}
