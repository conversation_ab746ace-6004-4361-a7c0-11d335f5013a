import { NextRequest, NextResponse } from "next/server"
import { getAdminInstance } from "@/lib/firebase-admin"
import { NotificationService } from "@/lib/domains/notification/notification.service"
import { sendEmail } from "@/lib/server/email-service"
import { EmailTemplates } from "@/lib/server/email-templates"

// Rate limiting and origin validation for security
const ALLOWED_ORIGINS = [
  "https://vercel.com",
  "https://cron-job.vercel.app",
  process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : null,
].filter(Bo<PERSON>an)

const CRON_SECRET = process.env.CRON_SECRET || "default-secret"

/**
 * Validate cron job request for security
 */
function validateCronRequest(request: NextRequest): { isValid: boolean; error?: string } {
  // Validate cron secret for security
  const authHeader = request.headers.get("authorization")
  const providedSecret = authHeader?.replace("Bearer ", "")
  
  if (providedSecret !== CRON_SECRET) {
    console.error("Unauthorized cron job access attempt")
    return { isValid: false, error: "Unauthorized" }
  }

  // Validate origin for additional security
  const origin = request.headers.get("origin")
  const userAgent = request.headers.get("user-agent")
  
  // Allow Vercel cron jobs (they don't always have origin headers)
  const isVercelCron = userAgent?.includes("vercel") || !origin
  const isAllowedOrigin = origin && ALLOWED_ORIGINS.includes(origin)
  
  if (!isVercelCron && !isAllowedOrigin) {
    console.error("Invalid origin for cron job:", origin)
    return { isValid: false, error: "Invalid origin" }
  }

  return { isValid: true }
}

/**
 * Complete trips that should be marked as completed (active → completed)
 * This function is idempotent and safe to run multiple times
 */
async function completeTrips() {
  console.log("Starting trip completion process...")

  // Get Firebase Admin instance
  const { adminDb } = await getAdminInstance()
  if (!adminDb) {
    throw new Error("Firebase Admin not initialized")
  }

  // Get current date in UTC
  const now = new Date()
  const currentDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())

  const tripsRef = adminDb.collection("trips")
  const completedTrips: any[] = []
  const batch = adminDb.batch()

  // Check for trips that should be completed (active → completed)
  console.log("Checking for trips to complete...")
  const activeTripsQuery = tripsRef.where("status", "==", "active")
  const activeTripsSnapshot = await activeTripsQuery.get()

  // Process each active trip
  for (const tripDoc of activeTripsSnapshot.docs) {
    const tripData = tripDoc.data()
    const tripId = tripDoc.id

    // Convert Firestore timestamp to Date
    const endDate = tripData.endDate?.toDate()
    if (!endDate) continue

    // Check if trip should be completed (endDate < current date)
    const tripEndDate = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate())

    if (tripEndDate < currentDate) {
      console.log(`Marking trip ${tripId} as completed (ended: ${tripEndDate.toISOString()})`)

      // Update trip status to completed
      const tripRef = adminDb.collection("trips").doc(tripId)
      batch.update(tripRef, {
        status: "completed",
        updatedAt: now,
      })

      // Get trip attendees for notifications
      const userTripsRef = adminDb.collection("userTrips")
      const userTripsQuery = userTripsRef
        .where("tripId", "==", tripId)
        .where("status", "==", "going")
      const userTripsSnapshot = await userTripsQuery.get()

      const attendeeIds = userTripsSnapshot.docs.map((doc: any) => doc.data().userId)

      completedTrips.push({
        tripId,
        tripName: tripData.name || "Unknown Trip",
        destination: tripData.destination || "Unknown Destination",
        squadId: tripData.squadId,
        attendeeIds,
      })
    }
  }

  // Commit the batch update (idempotent - safe to run multiple times)
  if (completedTrips.length > 0) {
    await batch.commit()
    console.log(`Updated ${completedTrips.length} trips to completed status`)
  }

  // Send notifications and emails for completed trips
  const completionNotificationResults = []
  const completionEmailResults = []

  for (const trip of completedTrips) {
    try {
      // Create notifications for all trip members
      for (const userId of trip.attendeeIds) {
        try {
          await NotificationService.createNotification(userId, {
            userId,
            type: "trip_completed",
            title: "Trip Completed! 🎉",
            message: `Your trip to ${trip.destination} has been completed. Share your experience by reviewing the trip!`,
            read: false,
            actionUrl: `/trips/${trip.tripId}/review`,
            relatedEntityId: trip.tripId,
            relatedEntityType: "trip",
          })
        } catch (notifError) {
          console.error(`Error creating notification for user ${userId}:`, notifError)
        }
      }

      completionNotificationResults.push({
        tripId: trip.tripId,
        notificationsSent: trip.attendeeIds.length,
      })

      // Get user details for email sending
      const userDocs = await Promise.all(
        trip.attendeeIds.map((userId: string) => adminDb.collection("users").doc(userId).get())
      )

      const users = userDocs
        .filter((doc) => doc.exists)
        .map((doc) => ({ id: doc.id, ...doc.data() }))

      // Send emails to all trip members
      for (const user of users) {
        try {
          const emailResult = await sendEmail({
            to: user.email,
            subject: "Trip Completed! Time to Review Your Experience! 🌟",
            templateId: EmailTemplates.TRIP_COMPLETED,
            params: {
              tripName: trip.tripName,
              destination: trip.destination,
              userName: user.displayName || user.email,
              reviewUrl: `${process.env.NEXT_PUBLIC_APP_URL}/trips/${trip.tripId}/review`,
            },
          })

          if (emailResult.success) {
            completionEmailResults.push({
              userId: user.id,
              email: user.email,
              messageId: emailResult.messageId,
            })
          } else {
            console.error(`Failed to send email to ${user.email}:`, emailResult.error)
          }
        } catch (emailError) {
          console.error(`Error sending email to ${user.email}:`, emailError)
        }
      }
    } catch (error) {
      console.error(`Error processing notifications/emails for trip ${trip.tripId}:`, error)
    }
  }

  return {
    tripsProcessed: activeTripsSnapshot.size,
    tripsCompleted: completedTrips.length,
    completedTrips: completedTrips.map((t) => ({
      tripId: t.tripId,
      destination: t.destination,
      attendeeCount: t.attendeeIds.length,
    })),
    completionNotificationResults,
    completionEmailResults,
  }
}

export async function GET(request: NextRequest) {
  try {
    const validation = validateCronRequest(request)
    if (!validation.isValid) {
      return NextResponse.json({ error: validation.error }, { status: validation.error === "Unauthorized" ? 401 : 403 })
    }

    const result = await completeTrips()

    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      ...result,
    }

    console.log("Trip completion completed:", response)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error in trip completion cron job:", error)
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}

// Also handle POST requests for manual testing
export async function POST(request: NextRequest) {
  return GET(request)
}
